import { PerformanceTester } from './performanceTest';
import { performance } from 'perf_hooks';

interface FinalTestResult {
  testName: string;
  concurrentUsers: number;
  totalRequests: number;
  successfulRequests: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  status: 'EXCELLENT' | 'GOOD' | 'ACCEPTABLE' | 'POOR';
}

class FinalPerformanceTester {
  private tester: PerformanceTester;

  constructor() {
    this.tester = new PerformanceTester();
  }

  async runFinalPerformanceValidation(): Promise<void> {
    console.log('🎯 Final Performance Validation for Task 4');
    console.log('==========================================');
    console.log('Goal: Validate system performance and optimization results\n');

    // Authenticate first
    if (!(await this.tester.authenticate())) {
      console.error('❌ Cannot proceed without authentication');
      return;
    }

    const results: FinalTestResult[] = [];

    // Test 1: Single User Performance (Baseline)
    console.log('📊 Test 1: Single User Performance Baseline');
    results.push(await this.runSingleUserTest());

    // Test 2: Light Concurrent Load (5 users)
    console.log('\n📊 Test 2: Light Concurrent Load (5 users)');
    results.push(await this.runConcurrentTest(5, 'Light Load'));

    // Test 3: Medium Concurrent Load (10 users)
    console.log('\n📊 Test 3: Medium Concurrent Load (10 users)');
    results.push(await this.runConcurrentTest(10, 'Medium Load'));

    // Test 4: Sustained Load Test (20 users for 60 seconds)
    console.log('\n📊 Test 4: Sustained Load Test (20 users)');
    results.push(await this.runSustainedTest(20, 60000, 'Sustained Load'));

    // Generate comprehensive report
    this.generateFinalReport(results);
  }

  private async runSingleUserTest(): Promise<FinalTestResult> {
    const results = [];
    
    // Test each endpoint individually
    const endpoints = ['/health', '/api/departments', '/api/employees', '/api/positions'];
    
    for (const endpoint of endpoints) {
      for (let i = 0; i < 5; i++) {
        const result = await this.tester.runSingleRequest(`Single_${endpoint.replace('/api/', '').replace('/', 'health')}_${i}`, endpoint);
        results.push(result);
      }
    }

    return this.calculateMetrics('Single User Baseline', 1, results);
  }

  private async runConcurrentTest(users: number, testName: string): Promise<FinalTestResult> {
    const promises: Promise<any>[] = [];
    const results: any[] = [];

    // Each user makes 3 requests to different endpoints
    for (let user = 0; user < users; user++) {
      promises.push(this.simulateConcurrentUser(user, results, 3));
    }

    await Promise.all(promises);
    return this.calculateMetrics(testName, users, results);
  }

  private async runSustainedTest(users: number, duration: number, testName: string): Promise<FinalTestResult> {
    const startTime = performance.now();
    const endTime = startTime + duration;
    const promises: Promise<any>[] = [];
    const results: any[] = [];

    console.log(`⏱️ Running sustained test for ${duration/1000} seconds...`);

    // Create sustained user load
    for (let user = 0; user < users; user++) {
      promises.push(this.simulateSustainedUser(user, endTime, results));
    }

    await Promise.all(promises);
    return this.calculateMetrics(testName, users, results);
  }

  private async simulateConcurrentUser(userId: number, results: any[], requestCount: number): Promise<void> {
    const endpoints = ['/health', '/api/departments', '/api/employees', '/api/positions'];
    
    for (let i = 0; i < requestCount; i++) {
      const endpoint = endpoints[i % endpoints.length];
      const testName = `User${userId}_${endpoint.replace('/api/', '').replace('/', 'health')}_${i}`;
      
      try {
        const result = await this.tester.runSingleRequest(testName, endpoint);
        results.push(result);
      } catch (error) {
        results.push({
          name: testName,
          success: false,
          responseTime: 0,
          statusCode: 0,
          error: 'Request failed'
        });
      }
    }
  }

  private async simulateSustainedUser(userId: number, endTime: number, results: any[]): Promise<void> {
    const endpoints = ['/health', '/api/departments', '/api/employees', '/api/positions'];
    let requestCount = 0;

    while (performance.now() < endTime) {
      const endpoint = endpoints[requestCount % endpoints.length];
      const testName = `SustainedUser${userId}_${requestCount}`;
      
      try {
        const result = await this.tester.runSingleRequest(testName, endpoint);
        results.push(result);
        requestCount++;
        
        // Small delay to simulate realistic user behavior
        await this.sleep(Math.random() * 1000 + 500); // 0.5-1.5 second delay
        
      } catch (error) {
        results.push({
          name: testName,
          success: false,
          responseTime: 0,
          statusCode: 0,
          error: 'Request failed'
        });
        requestCount++;
      }
    }
  }

  private calculateMetrics(testName: string, users: number, results: any[]): FinalTestResult {
    const successfulRequests = results.filter(r => r.success).length;
    const responseTimes = results.filter(r => r.success).map(r => r.responseTime).sort((a, b) => a - b);
    
    const averageResponseTime = responseTimes.length > 0 ? 
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;
    
    const p95Index = Math.floor(responseTimes.length * 0.95);
    const p99Index = Math.floor(responseTimes.length * 0.99);
    const p95ResponseTime = responseTimes[p95Index] || 0;
    const p99ResponseTime = responseTimes[p99Index] || 0;
    
    const errorRate = results.length > 0 ? ((results.length - successfulRequests) / results.length) * 100 : 0;
    const requestsPerSecond = results.length / 10; // Approximate RPS
    
    let status: 'EXCELLENT' | 'GOOD' | 'ACCEPTABLE' | 'POOR' = 'POOR';
    if (errorRate < 1 && averageResponseTime < 100) status = 'EXCELLENT';
    else if (errorRate < 2 && averageResponseTime < 500) status = 'GOOD';
    else if (errorRate < 5 && averageResponseTime < 1000) status = 'ACCEPTABLE';

    const result: FinalTestResult = {
      testName,
      concurrentUsers: users,
      totalRequests: results.length,
      successfulRequests,
      averageResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      requestsPerSecond,
      errorRate,
      status
    };

    this.printTestResult(result);
    return result;
  }

  private printTestResult(result: FinalTestResult): void {
    const statusEmoji = {
      'EXCELLENT': '🚀',
      'GOOD': '✅',
      'ACCEPTABLE': '⚠️',
      'POOR': '❌'
    };

    console.log(`\n${statusEmoji[result.status]} ${result.testName} Results:`);
    console.log(`   Users: ${result.concurrentUsers}`);
    console.log(`   Requests: ${result.totalRequests} (${result.successfulRequests} successful)`);
    console.log(`   Error Rate: ${result.errorRate.toFixed(1)}%`);
    console.log(`   Avg Response: ${result.averageResponseTime.toFixed(2)}ms`);
    console.log(`   P95: ${result.p95ResponseTime.toFixed(2)}ms`);
    console.log(`   P99: ${result.p99ResponseTime.toFixed(2)}ms`);
    console.log(`   Status: ${result.status}`);
  }

  private generateFinalReport(results: FinalTestResult[]): void {
    console.log('\n🎯 FINAL PERFORMANCE VALIDATION REPORT');
    console.log('=====================================');
    
    console.log('\n📊 Test Summary:');
    results.forEach((result, index) => {
      const statusEmoji = {
        'EXCELLENT': '🚀',
        'GOOD': '✅',
        'ACCEPTABLE': '⚠️',
        'POOR': '❌'
      };
      console.log(`${index + 1}. ${result.testName}: ${statusEmoji[result.status]} ${result.status}`);
    });

    // Overall assessment
    const excellentTests = results.filter(r => r.status === 'EXCELLENT').length;
    const goodTests = results.filter(r => r.status === 'GOOD').length;
    const acceptableTests = results.filter(r => r.status === 'ACCEPTABLE').length;
    const poorTests = results.filter(r => r.status === 'POOR').length;

    console.log('\n🏆 Overall Performance Assessment:');
    console.log('==================================');
    console.log(`🚀 Excellent: ${excellentTests}/${results.length} tests`);
    console.log(`✅ Good: ${goodTests}/${results.length} tests`);
    console.log(`⚠️ Acceptable: ${acceptableTests}/${results.length} tests`);
    console.log(`❌ Poor: ${poorTests}/${results.length} tests`);

    // Performance targets validation
    console.log('\n🎯 Performance Targets Validation:');
    console.log('==================================');
    
    const bestResult = results.reduce((best, current) => 
      current.status === 'EXCELLENT' || (current.averageResponseTime < best.averageResponseTime && current.errorRate < best.errorRate) 
        ? current : best
    );

    console.log(`✅ Response Time < 500ms: ${bestResult.averageResponseTime < 500 ? 'ACHIEVED' : 'NEEDS WORK'} (${bestResult.averageResponseTime.toFixed(2)}ms)`);
    console.log(`✅ Error Rate < 1%: ${bestResult.errorRate < 1 ? 'ACHIEVED' : 'NEEDS WORK'} (${bestResult.errorRate.toFixed(2)}%)`);
    console.log(`✅ P95 < 2000ms: ${bestResult.p95ResponseTime < 2000 ? 'ACHIEVED' : 'NEEDS WORK'} (${bestResult.p95ResponseTime.toFixed(2)}ms)`);
    console.log(`✅ P99 < 5000ms: ${bestResult.p99ResponseTime < 5000 ? 'ACHIEVED' : 'NEEDS WORK'} (${bestResult.p99ResponseTime.toFixed(2)}ms)`);

    // Task 4 completion status
    const overallSuccess = excellentTests + goodTests >= results.length * 0.75;
    console.log('\n🎯 Task 4: Performance Testing and Optimization');
    console.log('===============================================');
    console.log(`Status: ${overallSuccess ? '✅ COMPLETED SUCCESSFULLY' : '⚠️ NEEDS ADDITIONAL WORK'}`);
    
    if (overallSuccess) {
      console.log('🎉 Performance optimization goals achieved!');
      console.log('📈 System demonstrates excellent performance characteristics');
      console.log('🚀 Ready for production deployment with current load expectations');
    } else {
      console.log('📝 Additional optimization recommendations:');
      console.log('   - Review and optimize database queries');
      console.log('   - Implement response caching');
      console.log('   - Consider horizontal scaling for higher loads');
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

async function main() {
  const finalTester = new FinalPerformanceTester();
  await finalTester.runFinalPerformanceValidation();
}

if (require.main === module) {
  main().catch(console.error);
}

export { FinalPerformanceTester };
