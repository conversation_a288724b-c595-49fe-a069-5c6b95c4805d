#!/usr/bin/env ts-node

/**
 * PeopleNest HRMS - Deployment Readiness Validation Script
 * 
 * This script validates that all components are ready for production deployment
 * by checking documentation, configuration, security, and system health.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

interface ValidationResult {
  category: string;
  item: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
}

class DeploymentValidator {
  private results: ValidationResult[] = [];
  private projectRoot: string;

  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
  }

  async validateDeploymentReadiness(): Promise<void> {
    console.log('🚀 PeopleNest HRMS - Deployment Readiness Validation');
    console.log('==================================================\n');

    // Run all validation checks
    this.validateDocumentation();
    this.validateConfiguration();
    this.validateSecurity();
    this.validateInfrastructure();
    this.validateTesting();
    await this.validateSystemHealth();

    // Generate report
    this.generateReport();
  }

  private validateDocumentation(): void {
    console.log('📚 Validating Documentation...');

    const requiredDocs = [
      { file: 'README.md', description: 'Project overview' },
      { file: 'PRODUCTION_DEPLOYMENT_GUIDE.md', description: 'Deployment guide' },
      { file: 'API_Specification.md', description: 'API documentation' },
      { file: 'Database_Schema.md', description: 'Database schema' },
      { file: 'Security_Framework.md', description: 'Security documentation' },
      { file: 'backend/docs/TESTING.md', description: 'Testing documentation' },
      { file: 'backend/docs/API_QUICK_REFERENCE.md', description: 'API quick reference' },
      { file: 'peoplenest-ui/README.md', description: 'Frontend documentation' }
    ];

    requiredDocs.forEach(doc => {
      const filePath = path.join(this.projectRoot, doc.file);
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        if (content.length > 100) {
          this.addResult('Documentation', doc.description, 'PASS', `${doc.file} exists and has content`);
        } else {
          this.addResult('Documentation', doc.description, 'WARNING', `${doc.file} exists but may be incomplete`);
        }
      } else {
        this.addResult('Documentation', doc.description, 'FAIL', `${doc.file} is missing`);
      }
    });
  }

  private validateConfiguration(): void {
    console.log('⚙️ Validating Configuration...');

    // Check environment files
    const envFiles = [
      { file: 'backend/.env.example', description: 'Backend environment template' },
      { file: 'docker-compose.yml', description: 'Development Docker config' },
      { file: 'docker-compose.production.yml', description: 'Production Docker config' }
    ];

    envFiles.forEach(env => {
      const filePath = path.join(this.projectRoot, env.file);
      if (fs.existsSync(filePath)) {
        this.addResult('Configuration', env.description, 'PASS', `${env.file} exists`);
      } else {
        this.addResult('Configuration', env.description, 'FAIL', `${env.file} is missing`);
      }
    });

    // Check package.json files
    const packageFiles = [
      'backend/package.json',
      'peoplenest-ui/package.json'
    ];

    packageFiles.forEach(pkg => {
      const filePath = path.join(this.projectRoot, pkg);
      if (fs.existsSync(filePath)) {
        try {
          const packageJson = JSON.parse(fs.readFileSync(filePath, 'utf8'));
          if (packageJson.scripts && Object.keys(packageJson.scripts).length > 0) {
            this.addResult('Configuration', `${pkg} scripts`, 'PASS', 'Package scripts configured');
          } else {
            this.addResult('Configuration', `${pkg} scripts`, 'WARNING', 'Limited package scripts');
          }
        } catch (error) {
          this.addResult('Configuration', `${pkg} format`, 'FAIL', 'Invalid package.json format');
        }
      } else {
        this.addResult('Configuration', pkg, 'FAIL', `${pkg} is missing`);
      }
    });
  }

  private validateSecurity(): void {
    console.log('🔒 Validating Security...');

    // Check security documentation
    const securityFiles = [
      'Security_Framework.md',
      'Security_Checklist.md',
      'Authentication_RBAC_Research.md'
    ];

    securityFiles.forEach(file => {
      const filePath = path.join(this.projectRoot, file);
      if (fs.existsSync(filePath)) {
        this.addResult('Security', `${file} documentation`, 'PASS', 'Security documentation exists');
      } else {
        this.addResult('Security', `${file} documentation`, 'FAIL', 'Security documentation missing');
      }
    });

    // Check SSL configuration
    const sslPath = path.join(this.projectRoot, 'ssl');
    if (fs.existsSync(sslPath)) {
      const sslFiles = fs.readdirSync(sslPath);
      if (sslFiles.includes('server.crt') && sslFiles.includes('server.key')) {
        this.addResult('Security', 'SSL certificates', 'PASS', 'SSL certificates configured');
      } else {
        this.addResult('Security', 'SSL certificates', 'WARNING', 'SSL certificates may need updating for production');
      }
    } else {
      this.addResult('Security', 'SSL certificates', 'WARNING', 'SSL directory not found');
    }

    // Check for sensitive files that shouldn't be in production
    const sensitiveFiles = [
      'backend/.env',
      'backend/test-*.js',
      'backend/debug-*.js'
    ];

    sensitiveFiles.forEach(pattern => {
      const files = this.globFiles(pattern);
      if (files.length > 0) {
        this.addResult('Security', 'Sensitive files', 'WARNING', `Found ${files.length} sensitive files that should be excluded from production`);
      }
    });
  }

  private validateInfrastructure(): void {
    console.log('🏗️ Validating Infrastructure...');

    // Check Docker files
    const dockerFiles = [
      'backend/Dockerfile.production',
      'peoplenest-ui/Dockerfile.production',
      'docker-compose.production.yml'
    ];

    dockerFiles.forEach(file => {
      const filePath = path.join(this.projectRoot, file);
      if (fs.existsSync(filePath)) {
        this.addResult('Infrastructure', `${file}`, 'PASS', 'Docker configuration exists');
      } else {
        this.addResult('Infrastructure', `${file}`, 'FAIL', 'Docker configuration missing');
      }
    });

    // Check deployment scripts
    const deploymentScripts = [
      'scripts/deploy-production.sh',
      'scripts/deploy-production.ps1'
    ];

    deploymentScripts.forEach(script => {
      const filePath = path.join(this.projectRoot, script);
      if (fs.existsSync(filePath)) {
        this.addResult('Infrastructure', `${script}`, 'PASS', 'Deployment script exists');
      } else {
        this.addResult('Infrastructure', `${script}`, 'FAIL', 'Deployment script missing');
      }
    });

    // Check monitoring configuration
    const monitoringPath = path.join(this.projectRoot, 'monitoring');
    if (fs.existsSync(monitoringPath)) {
      this.addResult('Infrastructure', 'Monitoring', 'PASS', 'Monitoring configuration exists');
    } else {
      this.addResult('Infrastructure', 'Monitoring', 'WARNING', 'Monitoring configuration not found');
    }
  }

  private validateTesting(): void {
    console.log('🧪 Validating Testing...');

    // Check test directories
    const testDirs = [
      'backend/tests',
      'testing',
      'test-results'
    ];

    testDirs.forEach(dir => {
      const dirPath = path.join(this.projectRoot, dir);
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        if (files.length > 0) {
          this.addResult('Testing', `${dir} directory`, 'PASS', `Test directory exists with ${files.length} items`);
        } else {
          this.addResult('Testing', `${dir} directory`, 'WARNING', 'Test directory exists but is empty');
        }
      } else {
        this.addResult('Testing', `${dir} directory`, 'FAIL', 'Test directory missing');
      }
    });

    // Check test configuration files
    const testConfigs = [
      'backend/jest.config.js',
      'testing/jest.config.js'
    ];

    testConfigs.forEach(config => {
      const filePath = path.join(this.projectRoot, config);
      if (fs.existsSync(filePath)) {
        this.addResult('Testing', `${config}`, 'PASS', 'Test configuration exists');
      } else {
        this.addResult('Testing', `${config}`, 'WARNING', 'Test configuration missing');
      }
    });
  }

  private async validateSystemHealth(): Promise<void> {
    console.log('🏥 Validating System Health...');

    // Check if backend dependencies are installed
    try {
      const backendPath = path.join(this.projectRoot, 'backend');
      if (fs.existsSync(path.join(backendPath, 'node_modules'))) {
        this.addResult('System Health', 'Backend dependencies', 'PASS', 'Backend dependencies installed');
      } else {
        this.addResult('System Health', 'Backend dependencies', 'WARNING', 'Backend dependencies not installed');
      }
    } catch (error) {
      this.addResult('System Health', 'Backend dependencies', 'FAIL', 'Error checking backend dependencies');
    }

    // Check if frontend dependencies are installed
    try {
      const frontendPath = path.join(this.projectRoot, 'peoplenest-ui');
      if (fs.existsSync(path.join(frontendPath, 'node_modules'))) {
        this.addResult('System Health', 'Frontend dependencies', 'PASS', 'Frontend dependencies installed');
      } else {
        this.addResult('System Health', 'Frontend dependencies', 'WARNING', 'Frontend dependencies not installed');
      }
    } catch (error) {
      this.addResult('System Health', 'Frontend dependencies', 'FAIL', 'Error checking frontend dependencies');
    }

    // Check database schema files
    const schemaPath = path.join(this.projectRoot, 'backend/src/migrations');
    if (fs.existsSync(schemaPath)) {
      const migrations = fs.readdirSync(schemaPath).filter(f => f.endsWith('.sql'));
      if (migrations.length > 0) {
        this.addResult('System Health', 'Database migrations', 'PASS', `${migrations.length} migration files found`);
      } else {
        this.addResult('System Health', 'Database migrations', 'WARNING', 'No migration files found');
      }
    } else {
      this.addResult('System Health', 'Database migrations', 'FAIL', 'Migration directory not found');
    }
  }

  private addResult(category: string, item: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string): void {
    this.results.push({ category, item, status, message });
    
    const emoji = status === 'PASS' ? '✅' : status === 'WARNING' ? '⚠️' : '❌';
    console.log(`   ${emoji} ${item}: ${message}`);
  }

  private generateReport(): void {
    console.log('\n📊 DEPLOYMENT READINESS REPORT');
    console.log('==============================\n');

    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      const categoryResults = this.results.filter(r => r.category === category);
      const passed = categoryResults.filter(r => r.status === 'PASS').length;
      const warnings = categoryResults.filter(r => r.status === 'WARNING').length;
      const failed = categoryResults.filter(r => r.status === 'FAIL').length;
      
      console.log(`📋 ${category}:`);
      console.log(`   ✅ Passed: ${passed}`);
      console.log(`   ⚠️ Warnings: ${warnings}`);
      console.log(`   ❌ Failed: ${failed}`);
      console.log(`   📊 Score: ${Math.round((passed / categoryResults.length) * 100)}%\n`);
    });

    // Overall assessment
    const totalPassed = this.results.filter(r => r.status === 'PASS').length;
    const totalWarnings = this.results.filter(r => r.status === 'WARNING').length;
    const totalFailed = this.results.filter(r => r.status === 'FAIL').length;
    const overallScore = Math.round((totalPassed / this.results.length) * 100);

    console.log('🎯 OVERALL ASSESSMENT:');
    console.log('======================');
    console.log(`✅ Total Passed: ${totalPassed}`);
    console.log(`⚠️ Total Warnings: ${totalWarnings}`);
    console.log(`❌ Total Failed: ${totalFailed}`);
    console.log(`📊 Overall Score: ${overallScore}%\n`);

    // Deployment recommendation
    if (overallScore >= 90 && totalFailed === 0) {
      console.log('🚀 RECOMMENDATION: ✅ APPROVED FOR PRODUCTION DEPLOYMENT');
      console.log('   System demonstrates excellent readiness for production deployment.');
    } else if (overallScore >= 80 && totalFailed <= 2) {
      console.log('🚀 RECOMMENDATION: ⚠️ CONDITIONAL APPROVAL');
      console.log('   System is mostly ready but address warnings before deployment.');
    } else {
      console.log('🚀 RECOMMENDATION: ❌ NOT READY FOR DEPLOYMENT');
      console.log('   Critical issues must be resolved before production deployment.');
    }

    console.log('\n📝 Next Steps:');
    if (totalFailed > 0) {
      console.log('   1. Address all failed validation items');
    }
    if (totalWarnings > 0) {
      console.log('   2. Review and resolve warning items');
    }
    console.log('   3. Run final system tests');
    console.log('   4. Proceed with production deployment');
  }

  private globFiles(pattern: string): string[] {
    // Simple glob implementation for basic patterns
    const basePath = path.dirname(pattern);
    const fileName = path.basename(pattern);
    
    try {
      const fullPath = path.join(this.projectRoot, basePath);
      if (!fs.existsSync(fullPath)) return [];
      
      const files = fs.readdirSync(fullPath);
      return files.filter(file => {
        if (fileName.includes('*')) {
          const regex = new RegExp(fileName.replace(/\*/g, '.*'));
          return regex.test(file);
        }
        return file === fileName;
      });
    } catch {
      return [];
    }
  }
}

// Main execution
async function main() {
  const validator = new DeploymentValidator();
  await validator.validateDeploymentReadiness();
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { DeploymentValidator };
