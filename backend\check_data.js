const { Pool } = require('pg');

const pool = new Pool({
  host: '127.0.0.1',
  port: 5432,
  database: 'peoplenest',
  user: 'postgres',
  password: 'SecurePeopleNest2025!'
});

async function checkData() {
  try {
    // Check departments
    const deptResult = await pool.query('SELECT id, name, code, description FROM departments ORDER BY name');
    console.log('=== DEPARTMENTS ===');
    deptResult.rows.forEach(dept => {
      console.log(`${dept.id}: ${dept.name} (${dept.code}) - ${dept.description}`);
    });

    // Check positions
    const posResult = await pool.query('SELECT id, title, department_id FROM positions ORDER BY title');
    console.log('\n=== POSITIONS ===');
    posResult.rows.forEach(pos => {
      console.log(`${pos.id}: ${pos.title} (Dept: ${pos.department_id})`);
    });

    console.log('\n✅ Data check complete!');
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

checkData();
