import axios from 'axios';
import { performance } from 'perf_hooks';

interface TestResult {
  name: string;
  duration: number;
  success: boolean;
  responseTime: number;
  statusCode: number;
  error?: string;
}

interface PerformanceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
}

class PerformanceTester {
  private baseUrl: string;
  private results: TestResult[] = [];
  private authToken: string = '';

  constructor(baseUrl: string = 'http://localhost:3002') {
    this.baseUrl = baseUrl;
  }

  async authenticate(): Promise<boolean> {
    try {
      console.log('🔐 Authenticating...');
      const response = await axios.post(`${this.baseUrl}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'Password1234'
      });

      if (response.status === 200 && response.data.tokens?.accessToken) {
        this.authToken = response.data.tokens.accessToken;
        console.log('✅ Authentication successful');
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('❌ Authentication failed:', error.message);
      return false;
    }
  }

  async runSingleRequest(name: string, url: string, method: 'GET' | 'POST' = 'GET', data?: any): Promise<TestResult> {
    const startTime = performance.now();
    
    try {
      const config = {
        method,
        url: `${this.baseUrl}${url}`,
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        },
        data
      };

      const response = await axios(config);
      const endTime = performance.now();
      const duration = endTime - startTime;

      const result: TestResult = {
        name,
        duration,
        success: response.status >= 200 && response.status < 300,
        responseTime: duration,
        statusCode: response.status
      };

      this.results.push(result);
      return result;
    } catch (error: any) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      const result: TestResult = {
        name,
        duration,
        success: false,
        responseTime: duration,
        statusCode: error.response?.status || 0,
        error: error.message
      };

      this.results.push(result);
      return result;
    }
  }

  async runConcurrentRequests(name: string, url: string, concurrency: number, method: 'GET' | 'POST' = 'GET'): Promise<TestResult[]> {
    console.log(`🚀 Running ${concurrency} concurrent requests to ${url}...`);
    
    const promises = Array(concurrency).fill(null).map((_, i) => 
      this.runSingleRequest(`${name}_${i}`, url, method)
    );

    return Promise.all(promises);
  }

  async runLoadTest(duration: number = 30000): Promise<void> {
    console.log(`⏱️  Running load test for ${duration/1000} seconds...`);
    
    const startTime = Date.now();
    const endTime = startTime + duration;
    
    const endpoints = [
      { name: 'Health', url: '/health' },
      { name: 'Departments', url: '/api/departments' },
      { name: 'Employees', url: '/api/employees' },
      { name: 'Positions', url: '/api/positions' },
      { name: 'Audit', url: '/api/audit' }
    ];

    while (Date.now() < endTime) {
      const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)];
      await this.runSingleRequest(endpoint.name, endpoint.url);
      
      // Small delay to simulate real user behavior
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
    }
  }

  calculateMetrics(): PerformanceMetrics {
    const successfulResults = this.results.filter(r => r.success);
    const responseTimes = this.results.map(r => r.responseTime).sort((a, b) => a - b);
    
    const totalRequests = this.results.length;
    const successfulRequests = successfulResults.length;
    const failedRequests = totalRequests - successfulRequests;
    
    const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / totalRequests;
    const p95Index = Math.floor(responseTimes.length * 0.95);
    const p99Index = Math.floor(responseTimes.length * 0.99);
    
    const p95ResponseTime = responseTimes[p95Index] || 0;
    const p99ResponseTime = responseTimes[p99Index] || 0;
    
    // Calculate RPS based on test duration
    const testDuration = Math.max(...this.results.map(r => r.duration)) / 1000;
    const requestsPerSecond = totalRequests / testDuration;
    
    const errorRate = (failedRequests / totalRequests) * 100;

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      requestsPerSecond,
      errorRate
    };
  }

  printResults(): void {
    const metrics = this.calculateMetrics();

    console.log('\n📊 Performance Test Results');
    console.log('================================');
    console.log(`Total Requests: ${metrics.totalRequests}`);
    console.log(`Successful: ${metrics.successfulRequests}`);
    console.log(`Failed: ${metrics.failedRequests}`);
    console.log(`Error Rate: ${metrics.errorRate.toFixed(2)}%`);
    console.log(`Average Response Time: ${metrics.averageResponseTime.toFixed(2)}ms`);
    console.log(`95th Percentile: ${metrics.p95ResponseTime.toFixed(2)}ms`);
    console.log(`99th Percentile: ${metrics.p99ResponseTime.toFixed(2)}ms`);
    console.log(`Requests/Second: ${metrics.requestsPerSecond.toFixed(2)}`);

    // Show failed requests details
    const failedResults = this.results.filter(r => !r.success);
    if (failedResults.length > 0) {
      console.log('\n❌ Failed Requests Details');
      console.log('================================');
      failedResults.forEach(result => {
        console.log(`${result.name}: ${result.statusCode} - ${result.error || 'Unknown error'}`);
      });
    }

    // Performance thresholds
    console.log('\n🎯 Performance Thresholds');
    console.log('================================');
    console.log(`✅ Error Rate < 1%: ${metrics.errorRate < 1 ? 'PASS' : 'FAIL'}`);
    console.log(`✅ Avg Response < 1000ms: ${metrics.averageResponseTime < 1000 ? 'PASS' : 'FAIL'}`);
    console.log(`✅ P95 < 2000ms: ${metrics.p95ResponseTime < 2000 ? 'PASS' : 'FAIL'}`);
    console.log(`✅ P99 < 5000ms: ${metrics.p99ResponseTime < 5000 ? 'PASS' : 'FAIL'}`);
    console.log(`✅ RPS > 10: ${metrics.requestsPerSecond > 10 ? 'PASS' : 'FAIL'}`);
  }

  async runComprehensiveTest(): Promise<void> {
    console.log('🚀 Starting Comprehensive Performance Test');
    console.log('==========================================');
    
    // Authenticate first
    if (!(await this.authenticate())) {
      console.error('❌ Cannot proceed without authentication');
      return;
    }

    // 1. Single request tests
    console.log('\n1️⃣ Single Request Tests');
    await this.runSingleRequest('Health Check', '/health');
    await this.runSingleRequest('Departments', '/api/departments');
    await this.runSingleRequest('Employees', '/api/employees');
    await this.runSingleRequest('Positions', '/api/positions');

    // 2. Concurrent request tests
    console.log('\n2️⃣ Concurrent Request Tests');
    await this.runConcurrentRequests('Concurrent_Departments', '/api/departments', 10);
    await this.runConcurrentRequests('Concurrent_Employees', '/api/employees', 5);

    // 3. Load test
    console.log('\n3️⃣ Load Test (30 seconds)');
    await this.runLoadTest(30000);

    // Print results
    this.printResults();
  }
}

async function main() {
  const tester = new PerformanceTester();
  await tester.runComprehensiveTest();
}

if (require.main === module) {
  main().catch(console.error);
}

export { PerformanceTester };
