# PeopleNest HRMS - Frontend Application

## 📋 Overview

The PeopleNest frontend is a modern, responsive React application built with Next.js 14, TypeScript, and Tailwind CSS. It provides an intuitive interface for HR management, employee self-service, and administrative functions.

## 🏗️ Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives
- **State Management**: React Context + Custom hooks
- **Authentication**: JWT-based with secure session management
- **Theme**: Dark/Light mode support with semantic color variables

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Backend API running on port 3002

### Development Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Open browser
# Navigate to http://localhost:3000
```

### Environment Configuration

Create a `.env.local` file:

```bash
NEXT_PUBLIC_API_URL=http://localhost:3002
NEXT_PUBLIC_APP_NAME=PeopleNest HRMS
NEXT_PUBLIC_VERSION=1.0.0
```

## 🎨 Features

### Core Functionality

- **Dashboard**: Executive and departmental analytics
- **Employee Management**: Comprehensive employee profiles and management
- **Department Management**: Organizational structure management
- **Position Management**: Job roles and hierarchy
- **Authentication**: Secure login with role-based access
- **User Profile**: Personal settings and preferences

### UI/UX Features

- **Responsive Design**: Mobile-first approach
- **Dark/Light Theme**: Automatic and manual theme switching
- **Accessibility**: WCAG 2.1 AA compliant
- **Progressive Web App**: PWA capabilities with offline support
- **Real-time Updates**: Live data synchronization

### Security Features

- **Role-Based Access Control**: 5-tier permission system
- **Secure Authentication**: JWT token management
- **Input Validation**: Client-side and server-side validation
- **CSRF Protection**: Cross-site request forgery prevention

## 📁 Project Structure

```text
peoplenest-ui/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (auth)/            # Authentication pages
│   │   ├── dashboard/         # Dashboard pages
│   │   ├── employees/         # Employee management
│   │   ├── departments/       # Department management
│   │   └── layout.tsx         # Root layout
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Base UI components
│   │   ├── forms/            # Form components
│   │   └── layout/           # Layout components
│   ├── lib/                  # Utility functions and configurations
│   ├── hooks/                # Custom React hooks
│   ├── types/                # TypeScript type definitions
│   └── styles/               # Global styles and Tailwind config
├── public/                   # Static assets
└── docs/                     # Component documentation
```

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Run component tests
npm run test:components
```

## 🎯 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized for excellent user experience
- **Bundle Size**: Optimized with code splitting and tree shaking
- **Image Optimization**: Next.js Image component with WebP support

## 🔧 Development Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
npm run format       # Format code with Prettier
```

## 🚀 Deployment

### Production Build

```bash
npm run build
npm run start
```

### Docker Deployment

```bash
# Build Docker image
docker build -f Dockerfile.production -t peoplenest-frontend .

# Run container
docker run -p 3000:3000 peoplenest-frontend
```

### Environment Variables (Production)

```bash
NEXT_PUBLIC_API_URL=https://api.yourcompany.com
NEXT_PUBLIC_APP_NAME=PeopleNest HRMS
NEXT_PUBLIC_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=production
```

## 📖 Documentation

- **Component Library**: See `/docs/components.md`
- **Theme System**: See `/docs/theming.md`
- **API Integration**: See `/docs/api-integration.md`
- **Deployment Guide**: See `../PRODUCTION_DEPLOYMENT_GUIDE.md`

## 🤝 Contributing

1. Follow the established code style and conventions
2. Write tests for new features
3. Update documentation as needed
4. Ensure accessibility compliance
5. Test across different browsers and devices

## 📄 License

This project is part of the PeopleNest HRMS system and is licensed under the MIT License.
