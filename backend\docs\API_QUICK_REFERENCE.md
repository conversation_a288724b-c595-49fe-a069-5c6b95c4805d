# 🚀 PeopleNest HRMS - API Quick Reference

## 📋 Overview

This document provides a quick reference for the PeopleNest HRMS API endpoints, authentication, and common usage patterns.

**Base URL**: `http://localhost:3002` (Development) | `https://api.yourcompany.com` (Production)  
**API Version**: v1  
**Authentication**: JWT Bearer Token

## 🔐 Authentication

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Password1234"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User",
    "role": "super_admin"
  }
}
```

### Using Authentication
Include the JWT token in all subsequent requests:
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 👥 Employee Management

### Get All Employees
```http
GET /api/employees
Authorization: Bearer {token}
```

### Get Employee by ID
```http
GET /api/employees/{id}
Authorization: Bearer {token}
```

### Create Employee
```http
POST /api/employees
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "departmentId": "dept-uuid",
  "positionId": "pos-uuid",
  "startDate": "2024-01-15",
  "salary": 75000,
  "status": "active"
}
```

### Update Employee
```http
PUT /api/employees/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Smith",
  "salary": 80000
}
```

### Delete Employee
```http
DELETE /api/employees/{id}
Authorization: Bearer {token}
```

## 🏢 Department Management

### Get All Departments
```http
GET /api/departments
Authorization: Bearer {token}
```

### Create Department
```http
POST /api/departments
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Engineering",
  "description": "Software development and technical operations",
  "managerId": "manager-uuid",
  "budget": 1000000,
  "location": "San Francisco"
}
```

### Update Department
```http
PUT /api/departments/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Software Engineering",
  "budget": 1200000
}
```

## 💼 Position Management

### Get All Positions
```http
GET /api/positions
Authorization: Bearer {token}
```

### Create Position
```http
POST /api/positions
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Senior Software Engineer",
  "departmentId": "dept-uuid",
  "level": "senior",
  "minSalary": 90000,
  "maxSalary": 130000,
  "description": "Lead software development projects",
  "requirements": ["5+ years experience", "React/Node.js"]
}
```

## 📊 Dashboard & Analytics

### Get Dashboard Data
```http
GET /api/dashboard
Authorization: Bearer {token}
```

**Response:**
```json
{
  "totalEmployees": 150,
  "totalDepartments": 8,
  "totalPositions": 25,
  "recentHires": 5,
  "departmentStats": [...],
  "performanceMetrics": {...}
}
```

## 🔍 Search & Filtering

### Search Employees
```http
GET /api/employees?search=john&department=engineering&status=active
Authorization: Bearer {token}
```

### Filter with Pagination
```http
GET /api/employees?page=1&limit=20&sortBy=lastName&sortOrder=asc
Authorization: Bearer {token}
```

## 📋 Audit & Logging

### Get Audit Logs
```http
GET /api/audit
Authorization: Bearer {token}
```

### Get User Activity
```http
GET /api/audit/user/{userId}
Authorization: Bearer {token}
```

## 🏥 Health Check

### System Health
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-12-26T10:30:00Z",
  "version": "1.0.0",
  "database": "connected",
  "redis": "connected"
}
```

## 📝 Common Response Formats

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Validation Error",
  "message": "Email is required",
  "code": "VALIDATION_ERROR",
  "details": {...}
}
```

### Paginated Response
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8
  }
}
```

## 🔒 Role-Based Access Control

### Permission Levels
1. **super_admin**: Full system access
2. **hr_admin**: HR management functions
3. **manager**: Department and team management
4. **hr_user**: Basic HR operations
5. **employee**: Self-service only

### Endpoint Permissions
- **Public**: `/health`, `/api/auth/login`
- **Employee+**: `/api/employees/{own-id}`, `/api/profile`
- **HR User+**: `/api/employees` (read), `/api/departments` (read)
- **Manager+**: Department management, team member management
- **HR Admin+**: Full employee/department CRUD
- **Super Admin**: All endpoints including system administration

## 🚨 Error Codes

| Code | Description |
|------|-------------|
| `UNAUTHORIZED` | Invalid or missing authentication |
| `FORBIDDEN` | Insufficient permissions |
| `VALIDATION_ERROR` | Request validation failed |
| `NOT_FOUND` | Resource not found |
| `CONFLICT` | Resource already exists |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `INTERNAL_ERROR` | Server error |

## 📊 Rate Limiting

- **Authentication**: 3 attempts per 15 minutes
- **API Requests**: 1000 requests per minute (configurable)
- **File Uploads**: 10 uploads per minute

## 🔧 Development Tools

### Test Authentication
```bash
# Test login
curl -X POST http://localhost:3002/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password1234"}'

# Test authenticated endpoint
curl -X GET http://localhost:3002/api/employees \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Environment Variables
```bash
# Required for API functionality
JWT_SECRET=your-secret-key
DATABASE_URL=postgresql://user:pass@localhost:5432/peoplenest
REDIS_URL=redis://localhost:6379
```

## 📚 Additional Resources

- **Full API Specification**: See `../API_Specification.md`
- **Authentication Guide**: See `../Authentication_RBAC_Research.md`
- **Security Framework**: See `../Security_Framework.md`
- **Database Schema**: See `../Database_Schema.md`

---

**Last Updated**: December 26, 2025  
**API Version**: 1.0.0
