/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(rsc)/./src/components/providers/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBc0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxQZW9wbGVOZXN0XFxwZW9wbGVuZXN0LXVpXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0c31b8ade169\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcUGVvcGxlTmVzdFxccGVvcGxlbmVzdC11aVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMGMzMWI4YWRlMTY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-sans\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-sans\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-mono\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-mono\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(rsc)/./src/components/providers/theme-provider.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"PeopleNest - Enterprise HRMS Platform\",\n    description: \"Modern, AI-powered Human Resource Management System for enterprise organizations\",\n    keywords: [\n        \"HRMS\",\n        \"HR\",\n        \"Human Resources\",\n        \"Employee Management\",\n        \"Payroll\",\n        \"Performance\"\n    ],\n    manifest: \"/manifest.json\",\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"PeopleNest HRMS\"\n    },\n    formatDetection: {\n        telephone: false\n    },\n    other: {\n        \"mobile-web-app-capable\": \"yes\",\n        \"apple-mobile-web-app-capable\": \"yes\",\n        \"apple-mobile-web-app-status-bar-style\": \"default\",\n        \"apple-mobile-web-app-title\": \"PeopleNest\",\n        \"application-name\": \"PeopleNest HRMS\",\n        \"msapplication-TileColor\": \"#3b82f6\",\n        \"msapplication-config\": \"/browserconfig.xml\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    userScalable: false,\n    viewportFit: \"cover\",\n    themeColor: \"#3b82f6\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: `\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme-mode') || 'light';\n                  var root = document.documentElement;\n\n                  // Apply theme immediately to prevent hydration mismatch\n                  if (theme === 'system') {\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\n                  } else {\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\n                  }\n                } catch (e) {\n                  // Fallback to light theme if anything goes wrong\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\n                }\n              })();\n            `\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_sans_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_mono_subsets_latin_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} font-sans antialiased touch-manipulation`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              (function() {\n                try {\n                  var theme = localStorage.getItem('theme-mode') || 'light';\n                  var root = document.documentElement;\n\n                  // Apply theme immediately to prevent hydration mismatch\n                  if (theme === 'system') {\n                    var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + systemTheme;\n                  } else {\n                    root.className = root.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' ' + theme;\n                  }\n                } catch (e) {\n                  // Fallback to light theme if anything goes wrong\n                  document.documentElement.className = document.documentElement.className.replace(/\\\\b(light|dark)\\\\b/g, '').trim() + ' light';\n                }\n              })();\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx",
"useTheme",
);const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\PeopleNest\\peoplenest-ui\\src\\components\\providers\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/theme-provider.tsx */ \"(ssr)/./src/components/providers/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQZW9wbGVOZXN0JTVDJTVDcGVvcGxlbmVzdC11aSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBc0YiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFBlb3BsZU5lc3RcXFxccGVvcGxlbmVzdC11aVxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPeopleNest%5C%5Cpeoplenest-ui%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Building2_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Simulate loading and redirect to dashboard\n            const timer = setTimeout({\n                \"Home.useEffect.timer\": ()=>{\n                    router.push(\"/dashboard\");\n                }\n            }[\"Home.useEffect.timer\"], 2000);\n            return ({\n                \"Home.useEffect\": ()=>clearTimeout(timer)\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], [\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-primary/5 via-background to-primary/10 flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.8\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        scale: 0.8\n                    },\n                    animate: {\n                        scale: 1\n                    },\n                    transition: {\n                        delay: 0.2,\n                        duration: 0.3\n                    },\n                    className: \"inline-flex items-center justify-center w-20 h-20 bg-primary rounded-3xl mb-6 shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"w-10 h-10 text-primary-foreground\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.4,\n                        duration: 0.3\n                    },\n                    className: \"text-4xl font-bold text-foreground mb-4\",\n                    children: \"PeopleNest\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        delay: 0.6,\n                        duration: 0.3\n                    },\n                    className: \"text-xl text-muted-foreground mb-8\",\n                    children: \"Enterprise HRMS Platform\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        delay: 0.8,\n                        duration: 0.3\n                    },\n                    className: \"flex items-center justify-center space-x-2 text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Loading your workspace...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/theme */ \"(ssr)/./src/lib/theme.ts\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Provide a fallback when used outside of ThemeProvider\n        return {\n            theme: 'system',\n            setTheme: ()=>{},\n            toggleTheme: ()=>{},\n            systemTheme: 'light',\n            actualTheme: 'light'\n        };\n    }\n    return context;\n}\nfunction ThemeProvider({ children, defaultTheme = 'light' }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [systemTheme, setSystemTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get the actual theme being applied\n    const actualTheme = theme === 'system' ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // Initialize theme on mount\n            const storedTheme = (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)();\n            setThemeState(storedTheme);\n            // Detect system theme\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n            // Set mounted first to prevent hydration mismatch\n            setMounted(true);\n            // Note: Theme is already applied by the script in root layout\n            // We don't need to apply it again here to prevent hydration issues\n            // Listen for system theme changes\n            const handleSystemThemeChange = {\n                \"ThemeProvider.useEffect.handleSystemThemeChange\": (e)=>{\n                    setSystemTheme(e.matches ? 'dark' : 'light');\n                    // If using system theme, reapply it\n                    if ((0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.getThemeMode)() === 'system') {\n                        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)('system');\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleSystemThemeChange\"];\n            mediaQuery.addEventListener('change', handleSystemThemeChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>{\n                    mediaQuery.removeEventListener('change', handleSystemThemeChange);\n                }\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const handleSetTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.setThemeMode)(newTheme);\n        (0,_lib_theme__WEBPACK_IMPORTED_MODULE_2__.applyTheme)(newTheme);\n    };\n    const handleToggleTheme = ()=>{\n        const currentActualTheme = theme === 'system' ? systemTheme : theme;\n        const newTheme = currentActualTheme === 'light' ? 'dark' : 'light';\n        handleSetTheme(newTheme);\n    };\n    // Note: We don't prevent rendering to avoid hydration mismatch\n    // The theme is already applied by the script in root layout\n    const value = {\n        theme,\n        setTheme: handleSetTheme,\n        toggleTheme: handleToggleTheme,\n        systemTheme,\n        actualTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\PeopleNest\\\\peoplenest-ui\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/theme.ts":
/*!**************************!*\
  !*** ./src/lib/theme.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyTheme: () => (/* binding */ applyTheme),\n/* harmony export */   getContrastTextColor: () => (/* binding */ getContrastTextColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getThemeMode: () => (/* binding */ getThemeMode),\n/* harmony export */   initializeTheme: () => (/* binding */ initializeTheme),\n/* harmony export */   setThemeMode: () => (/* binding */ setThemeMode),\n/* harmony export */   statusColors: () => (/* binding */ statusColors),\n/* harmony export */   themeColors: () => (/* binding */ themeColors),\n/* harmony export */   toggleTheme: () => (/* binding */ toggleTheme),\n/* harmony export */   useSystemTheme: () => (/* binding */ useSystemTheme)\n/* harmony export */ });\n// Theme utilities for PeopleNest HRMS\n// Provides consistent color management and theme switching capabilities\nconst themeColors = {\n    // Light mode colors\n    light: {\n        background: '#ffffff',\n        foreground: '#171717',\n        card: '#ffffff',\n        cardForeground: '#171717',\n        popover: '#ffffff',\n        popoverForeground: '#171717',\n        primary: '#3b82f6',\n        primaryForeground: '#ffffff',\n        secondary: '#f1f5f9',\n        secondaryForeground: '#0f172a',\n        muted: '#f1f5f9',\n        mutedForeground: '#64748b',\n        accent: '#f1f5f9',\n        accentForeground: '#0f172a',\n        destructive: '#ef4444',\n        destructiveForeground: '#ffffff',\n        border: '#e2e8f0',\n        input: '#e2e8f0',\n        ring: '#3b82f6',\n        // Enhanced semantic colors\n        textPrimary: '#171717',\n        textSecondary: '#64748b',\n        textTertiary: '#94a3b8',\n        textInverse: '#ffffff',\n        surfacePrimary: '#ffffff',\n        surfaceSecondary: '#f8fafc',\n        surfaceTertiary: '#f1f5f9',\n        surfaceHover: '#f8fafc',\n        surfaceActive: '#e2e8f0',\n        success: '#22c55e',\n        successForeground: '#ffffff',\n        warning: '#f59e0b',\n        warningForeground: '#ffffff',\n        info: '#3b82f6',\n        infoForeground: '#ffffff',\n        // Chart colors\n        chart1: '#3b82f6',\n        chart2: '#10b981',\n        chart3: '#f59e0b',\n        chart4: '#ef4444',\n        chart5: '#8b5cf6',\n        chart6: '#06b6d4',\n        chart7: '#84cc16',\n        chart8: '#f97316',\n        // Status colors\n        statusMeeting: '#3b82f6',\n        statusDeadline: '#ef4444',\n        statusEvent: '#10b981',\n        statusActive: '#22c55e',\n        statusPending: '#f59e0b',\n        statusInactive: '#6b7280'\n    },\n    // Dark mode colors\n    dark: {\n        background: '#0a0a0a',\n        foreground: '#ededed',\n        card: '#111111',\n        cardForeground: '#ededed',\n        popover: '#111111',\n        popoverForeground: '#ededed',\n        primary: '#60a5fa',\n        primaryForeground: '#0a0a0a',\n        secondary: '#1e293b',\n        secondaryForeground: '#ededed',\n        muted: '#1e293b',\n        mutedForeground: '#94a3b8',\n        accent: '#1e293b',\n        accentForeground: '#ededed',\n        destructive: '#dc2626',\n        destructiveForeground: '#ededed',\n        border: '#27272a',\n        input: '#27272a',\n        ring: '#60a5fa',\n        // Enhanced semantic colors\n        textPrimary: '#ededed',\n        textSecondary: '#a1a1aa',\n        textTertiary: '#71717a',\n        textInverse: '#0a0a0a',\n        surfacePrimary: '#0a0a0a',\n        surfaceSecondary: '#111111',\n        surfaceTertiary: '#1a1a1a',\n        surfaceHover: '#1e1e1e',\n        surfaceActive: '#27272a',\n        success: '#22c55e',\n        successForeground: '#0a0a0a',\n        warning: '#f59e0b',\n        warningForeground: '#0a0a0a',\n        info: '#60a5fa',\n        infoForeground: '#0a0a0a',\n        // Chart colors - adjusted for dark theme\n        chart1: '#60a5fa',\n        chart2: '#34d399',\n        chart3: '#fbbf24',\n        chart4: '#f87171',\n        chart5: '#a78bfa',\n        chart6: '#22d3ee',\n        chart7: '#a3e635',\n        chart8: '#fb923c',\n        // Status colors - adjusted for dark theme\n        statusMeeting: '#60a5fa',\n        statusDeadline: '#f87171',\n        statusEvent: '#34d399',\n        statusActive: '#34d399',\n        statusPending: '#fbbf24',\n        statusInactive: '#9ca3af'\n    }\n};\n/**\n * Utility function to get the current theme mode\n */ function getThemeMode() {\n    if (true) return 'system';\n    const stored = localStorage.getItem('theme-mode');\n    if (stored === 'light' || stored === 'dark') return stored;\n    return 'system';\n}\n/**\n * Utility function to set the theme mode\n */ function setThemeMode(mode) {\n    if (true) return;\n    if (mode === 'system') {\n        localStorage.removeItem('theme-mode');\n    } else {\n        localStorage.setItem('theme-mode', mode);\n    }\n}\n/**\n * Utility function to toggle between light and dark modes\n */ function toggleTheme() {\n    const current = getThemeMode();\n    const next = current === 'light' ? 'dark' : 'light';\n    setThemeMode(next);\n}\n/**\n * Hook to detect system theme preference\n */ function useSystemTheme() {\n    if (true) return 'light';\n    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n}\n/**\n * Apply theme to document element\n */ function applyTheme(mode) {\n    if (true) return;\n    const root = document.documentElement;\n    // Remove existing theme classes\n    root.classList.remove('light', 'dark');\n    if (mode === 'system') {\n        // Use system preference\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        root.classList.add(systemTheme);\n    } else {\n        // Use explicit theme\n        root.classList.add(mode);\n    }\n}\n/**\n * Initialize theme on app load\n */ function initializeTheme() {\n    if (true) return;\n    const stored = localStorage.getItem('theme-mode');\n    const mode = stored || 'system';\n    applyTheme(mode);\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = ()=>{\n        if (getThemeMode() === 'system') {\n            applyTheme('system');\n        }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return ()=>mediaQuery.removeEventListener('change', handleChange);\n}\n/**\n * Utility to get contrast-safe text color for a given background\n */ function getContrastTextColor(backgroundColor, theme = 'light') {\n    // Simple heuristic - in a real app you might want to use a proper contrast calculation\n    const colors = themeColors[theme];\n    // For dark backgrounds, use light text\n    if (backgroundColor.includes('dark') || backgroundColor.includes('black') || backgroundColor === colors.primary || backgroundColor === colors.destructive) {\n        return colors.textInverse;\n    }\n    // For light backgrounds, use dark text\n    return colors.textPrimary;\n}\n/**\n * Status color mappings for consistent UI\n */ const statusColors = {\n    active: {\n        light: 'bg-green-500/10 text-green-600',\n        dark: 'bg-green-500/10 text-green-400'\n    },\n    inactive: {\n        light: 'bg-muted text-muted-foreground',\n        dark: 'bg-muted text-muted-foreground'\n    },\n    pending: {\n        light: 'bg-yellow-500/10 text-yellow-600',\n        dark: 'bg-yellow-500/10 text-yellow-400'\n    },\n    approved: {\n        light: 'bg-green-500/10 text-green-600',\n        dark: 'bg-green-500/10 text-green-400'\n    },\n    rejected: {\n        light: 'bg-red-500/10 text-red-600',\n        dark: 'bg-red-500/10 text-red-400'\n    },\n    draft: {\n        light: 'bg-muted text-muted-foreground',\n        dark: 'bg-muted text-muted-foreground'\n    }\n};\n/**\n * Get status color classes for current theme\n */ function getStatusColor(status, theme = 'light') {\n    return statusColors[status][theme];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/theme.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPeopleNest%5Cpeoplenest-ui%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPeopleNest%5Cpeoplenest-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();